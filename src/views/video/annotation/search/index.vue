<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <!-- 工具栏自定义按钮 -->
    <template #toolbar:after>
      <!-- 无额外工具栏按钮 -->
    </template>

    <!-- 搜索类型列自定义渲染 -->
    <template #table:value2:simple="{ row }">
      <el-tag :type="getSearchTypeTagType(row.value2)">
        {{ row.value2 }}
      </el-tag>
    </template>

    <!-- 操作列自定义渲染 -->
    <template #table:action:after="{ row }">
      <el-button type="text" size="mini" @click="handleView(row)">查看</el-button>
      <el-button type="text" size="mini" @click="handleRefresh(row)">
        刷新
      </el-button>
      <el-button type="text" size="mini" @click="handleExport(row)">下载</el-button>
    </template>

    <template #info:before></template>
    <template #after></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { searchType } from '@/dicts/video/index.js'

export default {
  name: 'VideoAnnotationSearch',
  data() {
    return {
      tableType: 'video_annotation_search'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '搜索记录',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },

        model: {
          // 搜索ID
          value1: {
            type: 'text',
            label: '搜索ID',
            align: 'left',
            width: 120,
            fixed: 'left',
            search: {
              hidden: true
            }
          },
          // 搜索类型
          value2: {
            type: 'select',
            label: '搜索类型',
            width: 120,
            search: {
              type: 'select',
              placeholder: '请选择搜索类型',
              clearable: true,
              options: [
                { label: '全部类型', value: '' },
                ...searchType
              ]
            },
            options: searchType
          },
          // 搜索条件/特征值
          value3: {
            type: 'text',
            label: '搜索条件/特征值',
            showOverflowTooltip: true,
            search: {
              hidden: true
            }
          },
          // 关联视频流
          value4: {
            type: 'text',
            label: '关联视频流',
            width: 150,
            search: {
              type: 'input',
              placeholder: '请输入视频流名称'
            }
          },
          // 匹配结果数
          value5: {
            type: 'text',
            label: '匹配结果数',
            width: 120,
            search: {
              hidden: true
            }
          },
          // TOP1匹配度
          value6: {
            type: 'text',
            label: 'TOP1匹配度',
            width: 120,
            search: {
              hidden: true
            }
          },
          // 搜索时间
          value7: {
            type: 'datetime',
            label: '搜索时间',
            width: 160,
            search: {
              hidden: true
            }
          },
          // 搜索时间范围（搜索专用字段）
          searchTimeRange: {
            type: 'text',
            label: '搜索时间范围',
            table: { hidden: true },
            search: {
              type: 'date-time-range',
              props: {
                type: 'datetimerange',
                rangeSeparator: '至',
                startPlaceholder: '开始时间',
                endPlaceholder: '结束时间',
                format: 'yyyy-MM-dd HH:mm:ss',
                valueFormat: 'yyyy-MM-dd HH:mm:ss'
              }
            },
            form: { hidden: true }
          }
        }
      }
    }
  },
  methods: {
    // 获取搜索类型标签类型
    getSearchTypeTagType(searchType) {
      const typeMap = {
        '向量搜索': 'primary',
        '全文搜索': 'success',
        '混合搜索': 'warning'
      }
      return typeMap[searchType] || 'info'
    },

    // 获取下载或刷新按钮文本
    getDownloadOrRefreshText(row) {
      // 这里可以根据业务逻辑判断显示下载还是刷新
      // 示例：根据搜索ID的奇偶性来决定
      return parseInt(row.value1) % 2 === 0 ? '下载' : '刷新'
    },

    // 查看操作
    handleView(row) {
      this.$refs.sheetRef.handleInfo(row)
    },

    // 下载或刷新操作
    handleDownloadOrRefresh(row) {
      const action = this.getDownloadOrRefreshText(row)
      if (action === '下载') {
        this.handleDownload(row)
      } else {
        this.handleRefresh(row)
      }
    },

    // 下载操作
    handleDownload(row) {
      this.$modal.confirm(`确认要下载搜索记录"${row.value1}"的结果吗？`).then(() => {
        // 这里可以调用下载接口
        console.log('下载搜索结果:', row)
        this.$modal.msgSuccess('下载成功')
      }).catch(() => {})
    },

    // 刷新操作
    handleRefresh(row) {
      this.$modal.confirm(`确认要刷新搜索记录"${row.value1}"吗？`).then(() => {
        // 这里可以调用刷新接口
        console.log('刷新搜索记录:', row)
        this.$modal.msgSuccess('刷新成功')
        // 刷新列表
        this.$refs.sheetRef.getTableData()
      }).catch(() => {})
    },

    // 导出操作
    handleExport(row) {
      this.$modal.confirm(`确认要导出搜索记录"${row.value1}"的结果吗？`).then(() => {
        // 这里可以调用导出接口
        console.log('导出搜索结果:', row)
        this.$modal.msgSuccess('导出成功')
      }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}
</style>
